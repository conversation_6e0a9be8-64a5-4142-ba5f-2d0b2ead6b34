import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// {@template face_detection_service}
/// Service that handles face detection using Google ML Kit.
/// Provides real-time face detection and coverage analysis.
/// {@endtemplate}
class FaceDetectionService {
  /// {@macro face_detection_service}
  FaceDetectionService();

  final LoggerService _logger = LoggerService();

  FaceDetector? _faceDetector;
  bool _isInitialized = false;

  // Face guide configuration (oval area in center of screen)
  static const double _faceGuideWidthRatio = 0.7; // 70% of screen width
  static const double _faceGuideHeightRatio = 0.8; // 80% of screen height
  static const double _minimumCoverageThreshold = 80; // 80% coverage required

  // Face quality thresholds
  static const double _maxHeadRotationAngle = 15; // degrees
  static const double _minFaceSizeRatio = 0.1; // 10% of screen
  static const double _maxFaceSizeRatio = 0.8; // 80% of screen
  static const double _minEyeOpenProbability = 0.5; // 50% confidence
  static const int _maxFrameProcessingMs = 100; // 100ms max processing time

  // Frame processing statistics
  int _totalFramesProcessed = 0;
  int _framesWithValidFace = 0;
  DateTime? _lastFrameProcessTime;

  /// Whether the service is initialized and ready for use
  bool get isInitialized => _isInitialized;

  /// Initializes the face detection service and ML Kit detector
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service already initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection service initialization started',
      ),
    );

    try {
      // Configure face detector options
      final options = FaceDetectorOptions(
        enableContours: true,
        enableLandmarks: true,
      );

      _faceDetector = FaceDetector(options: options);
      _isInitialized = true;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service initialization completed',
          'Options: $options',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection service initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Processes an InputImage and detects faces using ML Kit
  ///
  /// This method performs real face detection on the provided InputImage
  /// and returns comprehensive face analysis results.
  Future<FaceDetectionResult?> processImage(InputImage inputImage) async {
    if (!_isInitialized) {
      throw StateError('Face detection service not initialized');
    }

    final startTime = DateTime.now();

    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection processing started',
          'Image size: ${inputImage.metadata?.size}',
        ),
      );

      // Process image with ML Kit face detector
      final faces = await _faceDetector!.processImage(inputImage);

      final processingTime =
          DateTime.now().difference(startTime).inMilliseconds;
      _totalFramesProcessed++;
      _lastFrameProcessTime = DateTime.now();

      // Log performance metrics
      if (processingTime > _maxFrameProcessingMs) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection processing slow',
            'Time: ${processingTime}ms, Threshold: ${_maxFrameProcessingMs}ms',
          ),
        );
      }

      // Convert ML Kit results to our format
      final result = _convertFacesToResult(faces, inputImage);

      if (result.isValidDetection) {
        _framesWithValidFace++;
      }

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection completed',
          'Faces: ${faces.length}, Coverage: '
              '${result.coveragePercentage.toStringAsFixed(1)}%, '
              'Time: ${processingTime}ms',
        ),
      );

      return result;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection processing failed: $error',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Converts ML Kit Face detection results to FaceDetectionResult format
  FaceDetectionResult _convertFacesToResult(
    List<Face> faces,
    InputImage inputImage,
  ) {
    final timestamp = DateTime.now();
    final imageSize = inputImage.metadata?.size;

    if (faces.isEmpty) {
      return FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: timestamp,
      );
    }

    final faceCount = faces.length;
    final primaryFace = faces.first;

    // Analyze face quality
    final faceQuality = _analyzeFaceQuality(primaryFace, imageSize);

    // Calculate face coverage within guide area
    double coveragePercentage = 0;
    if (imageSize != null) {
      coveragePercentage = _calculateRealFaceCoverage(
        primaryFace.boundingBox,
        Size(imageSize.width, imageSize.height),
      );
    }

    // Convert ML Kit bounding box to our format
    final boundingBox = FaceBoundingBox(
      left: primaryFace.boundingBox.left,
      top: primaryFace.boundingBox.top,
      width: primaryFace.boundingBox.width,
      height: primaryFace.boundingBox.height,
    );

    return FaceDetectionResult(
      faceDetected: true,
      faceCount: faceCount,
      coveragePercentage: coveragePercentage,
      timestamp: timestamp,
      boundingBox: boundingBox,
      confidence: faceQuality.overallConfidence,
    );
  }

  /// Analyzes the quality of a detected face
  FaceQuality _analyzeFaceQuality(Face face, ui.Size? imageSize) {
    var overallConfidence = 0.8; // Base confidence for ML Kit detection
    final issues = <String>[];

    // Check head rotation angles
    final headEulerAngleX = face.headEulerAngleX ?? 0;
    final headEulerAngleY = face.headEulerAngleY ?? 0;
    final headEulerAngleZ = face.headEulerAngleZ ?? 0;

    if (headEulerAngleX.abs() > _maxHeadRotationAngle ||
        headEulerAngleY.abs() > _maxHeadRotationAngle ||
        headEulerAngleZ.abs() > _maxHeadRotationAngle) {
      issues.add('Head rotation too high');
      overallConfidence -= 0.2;
    }

    // Check face size relative to image
    if (imageSize != null) {
      final faceArea = face.boundingBox.width * face.boundingBox.height;
      final imageArea = imageSize.width * imageSize.height;
      final faceSizeRatio = faceArea / imageArea;

      if (faceSizeRatio < _minFaceSizeRatio) {
        issues.add('Face too small');
        overallConfidence -= 0.3;
      } else if (faceSizeRatio > _maxFaceSizeRatio) {
        issues.add('Face too large');
        overallConfidence -= 0.2;
      }
    }

    // Check eye openness if available
    final leftEyeOpenProbability = face.leftEyeOpenProbability;
    final rightEyeOpenProbability = face.rightEyeOpenProbability;

    if (leftEyeOpenProbability != null && rightEyeOpenProbability != null) {
      if (leftEyeOpenProbability < _minEyeOpenProbability ||
          rightEyeOpenProbability < _minEyeOpenProbability) {
        issues.add('Eyes not sufficiently open');
        overallConfidence -= 0.1;
      }
    }

    return FaceQuality(
      overallConfidence: overallConfidence.clamp(0.0, 1.0),
      issues: issues,
      headRotationX: headEulerAngleX,
      headRotationY: headEulerAngleY,
      headRotationZ: headEulerAngleZ,
      leftEyeOpenProbability: leftEyeOpenProbability,
      rightEyeOpenProbability: rightEyeOpenProbability,
    );
  }

  /// Validates if the face coverage meets the minimum threshold
  bool validateCoverage(FaceDetectionResult result) {
    return result.faceDetected &&
        result.faceCount == 1 &&
        result.coveragePercentage >= _minimumCoverageThreshold;
  }

  /// Calculates real face coverage percentage within the oval guide area
  double _calculateRealFaceCoverage(ui.Rect faceBounds, Size screenSize) {
    // Calculate face guide area (centered oval)
    final guideWidth = screenSize.width * _faceGuideWidthRatio;
    final guideHeight = screenSize.height * _faceGuideHeightRatio;
    final guideLeft = (screenSize.width - guideWidth) / 2;
    final guideTop = (screenSize.height - guideHeight) / 2;

    // For oval intersection, we approximate using ellipse area calculation
    // Convert face rectangle to center and dimensions
    final faceCenterX = faceBounds.left + (faceBounds.width / 2);
    final faceCenterY = faceBounds.top + (faceBounds.height / 2);
    final faceWidth = faceBounds.width;
    final faceHeight = faceBounds.height;

    // Calculate guide oval center and radii
    final guideCenterX = guideLeft + (guideWidth / 2);
    final guideCenterY = guideTop + (guideHeight / 2);
    final guideRadiusX = guideWidth / 2;
    final guideRadiusY = guideHeight / 2;

    // Calculate intersection area between face rectangle and guide oval
    final intersectionArea = _calculateRectangleOvalIntersection(
      faceCenterX,
      faceCenterY,
      faceWidth,
      faceHeight,
      guideCenterX,
      guideCenterY,
      guideRadiusX,
      guideRadiusY,
    );

    // Calculate guide oval area
    final guideArea = math.pi * guideRadiusX * guideRadiusY;

    return (intersectionArea / guideArea) * 100.0;
  }

  /// Calculates intersection area between a rectangle and an oval
  double _calculateRectangleOvalIntersection(
    double rectCenterX,
    double rectCenterY,
    double rectWidth,
    double rectHeight,
    double ovalCenterX,
    double ovalCenterY,
    double ovalRadiusX,
    double ovalRadiusY,
  ) {
    // Simplified approximation: check how much of the rectangle
    // falls within the oval bounds
    final rectLeft = rectCenterX - (rectWidth / 2);
    final rectTop = rectCenterY - (rectHeight / 2);

    // Sample points within the rectangle and check if they're inside the oval
    const samplePoints = 100;
    var pointsInside = 0;

    for (var i = 0; i < samplePoints; i++) {
      final x = rectLeft + (rectWidth * (i % 10) / 9);
      final y = rectTop + (rectHeight * (i ~/ 10) / 9);

      // Check if point is inside oval using ellipse equation
      final normalizedX = (x - ovalCenterX) / ovalRadiusX;
      final normalizedY = (y - ovalCenterY) / ovalRadiusY;

      if ((normalizedX * normalizedX) + (normalizedY * normalizedY) <= 1) {
        pointsInside++;
      }
    }

    return (pointsInside / samplePoints) * (rectWidth * rectHeight);
  }

  /// Calculates face coverage percentage within the guide area
  ///
  /// This is a mock implementation. In a real implementation, this would
  /// calculate the intersection between the detected face bounding box
  /// and the face guide overlay area.
  double calculateFaceCoverage(FaceBoundingBox faceBounds, Size screenSize) {
    // Calculate face guide area (centered oval)
    final guideWidth = screenSize.width * _faceGuideWidthRatio;
    final guideHeight = screenSize.height * _faceGuideHeightRatio;
    final guideLeft = (screenSize.width - guideWidth) / 2;
    final guideTop = (screenSize.height - guideHeight) / 2;

    // Calculate intersection area
    final intersectionLeft = math.max(faceBounds.left, guideLeft);
    final intersectionTop = math.max(faceBounds.top, guideTop);
    final intersectionRight =
        math.min(faceBounds.right, guideLeft + guideWidth);
    final intersectionBottom =
        math.min(faceBounds.bottom, guideTop + guideHeight);

    if (intersectionLeft >= intersectionRight ||
        intersectionTop >= intersectionBottom) {
      return 0; // No intersection
    }

    final intersectionArea = (intersectionRight - intersectionLeft) *
        (intersectionBottom - intersectionTop);
    final guideArea = guideWidth * guideHeight;

    return (intersectionArea / guideArea) * 100.0;
  }

  /// Gets the current face detection configuration
  Map<String, dynamic> getConfiguration() {
    return {
      'isInitialized': _isInitialized,
      'faceGuideWidthRatio': _faceGuideWidthRatio,
      'faceGuideHeightRatio': _faceGuideHeightRatio,
      'minimumCoverageThreshold': _minimumCoverageThreshold,
      'detectorOptions': _faceDetector != null
          ? {
              'enableContours': true,
              'enableLandmarks': true,
              'enableClassification': false,
              'enableTracking': false,
              'minFaceSize': 0.1,
              'performanceMode': 'fast',
            }
          : null,
    };
  }

  /// Updates the face detection configuration
  Future<void> updateConfiguration(Map<String, dynamic> config) async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection configuration update requested',
        'Config: $config',
      ),
    );

    // In a real implementation, this would update detector settings
    // For now, we just log the configuration change
  }

  /// Generates real-time feedback message based on face detection result
  String generateFeedbackMessage(FaceDetectionResult result) {
    if (!result.faceDetected) {
      return 'Position your face in the center';
    }

    if (result.faceCount > 1) {
      return 'Multiple faces detected - ensure only one person';
    }

    if (result.coveragePercentage < 60) {
      return 'Move closer to the camera';
    }

    if (result.coveragePercentage > 95) {
      return 'Move back from the camera';
    }

    if (result.coveragePercentage < _minimumCoverageThreshold) {
      return 'Center your face in the oval guide';
    }

    return 'Perfect! Hold steady';
  }

  /// Gets frame processing statistics
  Map<String, dynamic> getProcessingStats() {
    final validFramePercentage = _totalFramesProcessed > 0
        ? (_framesWithValidFace / _totalFramesProcessed) * 100
        : 0.0;

    return {
      'totalFramesProcessed': _totalFramesProcessed,
      'framesWithValidFace': _framesWithValidFace,
      'validFramePercentage': validFramePercentage,
      'lastProcessTime': _lastFrameProcessTime?.toIso8601String(),
    };
  }

  /// Resets frame processing statistics
  void resetStats() {
    _totalFramesProcessed = 0;
    _framesWithValidFace = 0;
    _lastFrameProcessTime = null;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection statistics reset',
      ),
    );
  }

  /// Validates recording quality based on frame statistics
  bool validateRecordingQuality({double minimumValidFramePercentage = 80.0}) {
    final stats = getProcessingStats();
    final validFramePercentage = stats['validFramePercentage'] as double;

    final isValid = validFramePercentage >= minimumValidFramePercentage;

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Recording quality validation',
        'Valid frames: ${validFramePercentage.toStringAsFixed(1)}%, '
            'Required: $minimumValidFramePercentage%, '
            'Result: ${isValid ? 'PASS' : 'FAIL'}',
      ),
    );

    return isValid;
  }

  /// Disposes of the face detection service and releases resources
  Future<void> dispose() async {
    if (!_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service disposal skipped - not initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection service disposal started',
      ),
    );

    try {
      await _faceDetector?.close();
      _faceDetector = null;
      _isInitialized = false;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service disposal completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection service disposal error: $error',
        ),
        error,
        stackTrace,
      );
      // Don't rethrow disposal errors
    }
  }
}

/// {@template size}
/// Simple size class for width and height dimensions.
/// {@endtemplate}
class Size {
  /// {@macro size}
  const Size(this.width, this.height);

  /// Width dimension
  final double width;

  /// Height dimension
  final double height;

  @override
  String toString() => 'Size($width, $height)';
}

/// {@template face_quality}
/// Represents the quality analysis of a detected face.
/// {@endtemplate}
class FaceQuality {
  /// {@macro face_quality}
  const FaceQuality({
    required this.overallConfidence,
    required this.issues,
    this.headRotationX,
    this.headRotationY,
    this.headRotationZ,
    this.leftEyeOpenProbability,
    this.rightEyeOpenProbability,
  });

  /// Overall confidence score (0.0-1.0)
  final double overallConfidence;

  /// List of quality issues detected
  final List<String> issues;

  /// Head rotation angle X (up/down tilt)
  final double? headRotationX;

  /// Head rotation angle Y (left/right turn)
  final double? headRotationY;

  /// Head rotation angle Z (side tilt)
  final double? headRotationZ;

  /// Left eye open probability
  final double? leftEyeOpenProbability;

  /// Right eye open probability
  final double? rightEyeOpenProbability;

  /// Whether the face quality is acceptable
  bool get isAcceptable => overallConfidence >= 0.6 && issues.isEmpty;

  @override
  String toString() {
    return 'FaceQuality('
        'confidence: ${overallConfidence.toStringAsFixed(2)}, '
        'issues: $issues'
        ')';
  }
}
